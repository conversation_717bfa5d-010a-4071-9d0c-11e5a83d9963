<template>
  <q-page v-if="!isFetching" class="q-pa-md">
    <!-- 课程标题区域 -->
    <div class="row items-center justify-between q-mb-lg">
      <div class="text-h3">{{ courseTitle }}</div>
    </div>

    <!-- 没有模块时的提示 -->
    <div v-if="moduleChatbotList.length === 0" class="text-h6 text-grey-6 text-center q-pa-xl">
      No modules available for this course
    </div>

    <!-- 模块列表 -->
    <div v-for="module in moduleChatbotList" :key="module.module_id" class="q-mb-xl">
      <q-separator spaced />

      <!-- 模块标题 -->
      <div class="row items-center justify-between q-py-md">
        <div class="text-h4">{{ module.module_title }}</div>
      </div>

      <!-- 没有聊天机器人时的提示 -->
      <div
        v-if="
          (module.chatbots.length === 1 && !module.chatbots[0]?.chatbot_id) ||
          module.chatbots.length === 0
        "
        class="text-subtitle1 text-grey-6 text-center q-pa-md"
      >
        No chatbots available for this module
      </div>

      <!-- 聊天机器人卡片网格 -->
      <div class="row q-col-gutter-md">
        <div
          v-for="chatbot in getValidChatbots(module)"
          :key="chatbot.chatbot_id"
          class="col-12 col-sm-6 col-md-4"
        >
          <q-card flat bordered class="h-100">
            <q-card-section>
              <div class="text-h6 q-mb-md">{{ chatbot.chatbot_name }}</div>
              <div class="q-gutter-y-sm text-body2">
                <div class="row items-center">
                  <q-icon name="school" size="xs" class="q-mr-sm" />
                  <span>Course: {{ courseTitle }}</span>
                </div>
                <div class="row items-center">
                  <q-icon name="person" size="xs" class="q-mr-sm" />
                  <span>Teacher: {{ chatbot.creator_user_full_name }}</span>
                </div>
                <div class="row items-center">
                  <q-icon name="event" size="xs" class="q-mr-sm" />
                  <span>Created on: {{ chatbot.created_at }}</span>
                </div>
              </div>
            </q-card-section>

            <q-separator />

            <q-card-actions align="center">
              <q-btn
                round
                color="primary"
                icon="chat"
                size="sm"
                @click="
                  continuePreviousChat(chatbot.chatbot_id, module.module_id, module.module_title)
                "
              >
                <q-tooltip>Start to Chat</q-tooltip>
              </q-btn>
            </q-card-actions>
          </q-card>
        </div>
      </div>
    </div>
  </q-page>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { api } from 'boot/axios';
import { onMounted } from 'vue';

const route = useRoute();
const router = useRouter();

interface ChatbotItem {
  chatbot_id: string;
  chatbot_name: string;
  created_at: string;
  creator_user_id: string;
  creator_user_full_name: string;
}

interface ModuleChatbotItem {
  module_id: string;
  course_id: string;
  module_title: string;
  description: JSON;
  created_at: string;
  chatbots: ChatbotItem[];
}

const getStringParam = (param: string | string[]): string => {
  return Array.isArray(param) ? param[0] || '' : param;
};

const moduleChatbotList = ref<ModuleChatbotItem[]>([]);
const isFetching = ref<boolean>(true);
const courseId = ref<string>(getStringParam(route.params.courseId || ''));
const courseTitle = ref<string>('');

const continuePreviousChat = (chatbot_id: string, module_id: string, module_title: string) => {
  void router.push({
    path: `/chat/${chatbot_id}/session/latest`,
    query: {
      courseId: courseId.value,
      courseTitle: courseTitle.value,
      moduleId: module_id,
      moduleTitle: module_title,
    },
  });
};

const utcTimeToLocalTime = (utcTime: string): string => {
  return new Date(utcTime).toLocaleString();
};

// 添加一个函数来过滤有效的聊天机器人
const getValidChatbots = (module: ModuleChatbotItem): ChatbotItem[] => {
  return module.chatbots.filter((chatbot) => chatbot.chatbot_id);
};

const fetchCourseInfo = async () => {
  const response = await api.get('/course-info', {
    params: {
      course_id: courseId.value,
    },
  });
  courseTitle.value = response.data.course_title;
};

const fetchModuleChatbotList = async () => {
  const response = await api.get('/module-chatbot-list', {
    params: {
      course_id: courseId.value,
    },
  });
  moduleChatbotList.value = response.data;

  // Sort the Modules and Chatbots by created_at from small to large
  moduleChatbotList.value.sort(
    (a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime(),
  );
  moduleChatbotList.value.forEach((module) => {
    module.chatbots.sort(
      (a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime(),
    );
  });

  // Convert UTC time to local time
  moduleChatbotList.value.forEach((module) => {
    module.chatbots.forEach((chatbot) => {
      chatbot.created_at = utcTimeToLocalTime(chatbot.created_at);
    });
  });
};

watch(route, async (newRoute) => {
  isFetching.value = true;
  courseId.value = getStringParam(newRoute.params.courseId || '');

  await fetchCourseInfo();
  await fetchModuleChatbotList();
  isFetching.value = false;
});

onMounted(async () => {
  isFetching.value = true;
  await fetchCourseInfo();
  await fetchModuleChatbotList();
  isFetching.value = false;
});
</script>

<style scoped>
.h-100 {
  height: 100%;
}
</style>
