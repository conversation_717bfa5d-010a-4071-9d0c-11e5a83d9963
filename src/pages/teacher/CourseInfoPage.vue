<template>
  <div class="q-pa-md q-gutter-sm">
    <q-btn color="white" text-color="black" label="edit" @click="gotoCoursePage"/>    
    <q-btn color="white" text-color="black" label="module" @click="gotoCoursePage"/>    
  </div>
</template>

<script setup lang="ts">
import { useRouter, useRoute } from 'vue-router';
const router = useRouter();
const route = useRoute();
const gotoCoursePage = () => {
  // router.push('/student/course/' + route.params.courseId);
  console.log('test');
  
} 
</script>