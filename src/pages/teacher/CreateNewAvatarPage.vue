<template>
  <q-page class="q-pa-md">
    <div class="text-h6 q-pa-md row q-gutter-sm">
      <q-btn flat dense round icon="arrow_back" to="/teacher/manage-avatars">
        <q-tooltip>Back to Avatars</q-tooltip>
      </q-btn>
      <div>Create New Avatar</div>
    </div>

    <div class="q-pa-md">
      <div class="text-h6">Enter Avatar Information</div>
      <q-card>
        <q-card-section>
          <q-input
            v-model="form.name"
            label="Avatar Name"
            clearable
            type="text"
            :rules="[
              val => !!val || 'Avatar Name is required',
              val => val.length <= 20 || 'Name must be less than 20 characters'
            ]"
          />

          <q-input
            v-model="form.knowledge"
            label="Knowledge Description"
            clearable
            type="textarea"
            :rules="[
              val => !!val || 'Knowledge description is required',
              val => val.length <= 200 || 'Description must be less than 200 characters'
            ]"
          />
        </q-card-section>
      </q-card>
    </div>

    <div class="q-pa-md">
      <q-btn
        no-caps
        label="Create Avatar"
        @click="handleCreateAvatar"
        color="primary"
        :disable="!isFormValid"
      />
    </div>
  </q-page>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useQuasar } from 'quasar'
import { api } from 'boot/axios'
import { useRouter } from 'vue-router'

const router = useRouter()

const $q = useQuasar()

interface AvatarForm {
  name: string
  knowledge: string
}

const form = ref<AvatarForm>({
  name: '',
  knowledge: ''
})

const isFormValid = computed(() => {
  return !!form.value.name && !!form.value.knowledge
})

const handleCreateAvatar = async (): Promise<void> => {
  const avatarData: AvatarForm = {
    ...form.value,
  }

  try {
    const response = await api.post<{ message: string }>('/create_new_avatar', avatarData)

    console.log('Server response:', response.data)

    $q.notify({
      type: 'positive' as const,
      message: `Avatar "${form.value.name}" created successfully!`,
      position: 'top'
    })

    await router.push('/teacher/manage-avatars')
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  } catch (error: any) {
    // error type is 'any' because axios errors can be complex
    console.error('Error creating avatar:', error.response?.data || error.message)

    $q.notify({
      type: 'negative' as const,
      message: error.response?.data?.message || 'Failed to create avatar',
      position: 'top'
    })
  }
}
</script>
