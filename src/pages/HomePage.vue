<template>
  <q-page v-if="!isFetching" class="q-pa-md">
    <div class="text-h3 q-pa-md">Notifications</div>
    <div class="q-gutter-sm q-pa-md" v-if="isTeacher">
      <q-btn
        no-caps
        label="Create New Notification"
        color="black"
        text-color="white"
        @click="createNewNotification()"
      />
    </div>
    <div v-if="notificationList.length === 0" class="text-h6 q-pa-md">
      No notifications available
    </div>
    <q-card
      v-for="notification in notificationList"
      :key="notification.notification_id"
      class="q-mb-md"
    >
      <q-card-section>
        <div class="text-h6">{{ notification.notification_title }}</div>
        <div>Course: {{ notification.course_title }}</div>
        <div>Teacher: {{ notification.creator_user_full_name }}</div>
        <div>Published on: {{ notification.created_at }}</div>
        <div>Content: {{ notification.description.content }}</div>
        <q-btn
          v-if="isTeacher"
          no-caps
          label="Delete this Notification"
          color="red"
          text-color="white"
          @click="deleteCurrentNotification(notification)"
        />
      </q-card-section>
    </q-card>
  </q-page>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useQuasar } from 'quasar';
import { api } from 'boot/axios';
import { onMounted } from 'vue';

const route = useRoute();
const router = useRouter();
const $q = useQuasar();

const isTeacher = ref<boolean>(false);

interface NotificationDescriptionItem {
  content: string;
}

interface NotificationItem {
  student_id: string;
  group_id: string;
  course_id: string;
  course_title: string;
  creator_user_id: string;
  creator_user_full_name: string;
  notification_id: string;
  notification_title: string;
  description: NotificationDescriptionItem;
  created_at: string;
  updated_at: string;
  deleted_at: string;
}

const notificationList = ref<NotificationItem[]>([]);

const isFetching = ref<boolean>(true);

const createNewNotification = async () => {
  // Redirect to the create new notification page
  await router.push('/teacher/notification/new');
};

const deleteCurrentNotification = (notification: NotificationItem) => {
  try {
    $q.dialog({
      title: 'Delete Notification',
      message:
        'Are you sure you want to delete this notification (' +
        notification.notification_title +
        ')?',
      ok: 'Yes',
      cancel: 'No',
    }).onOk(() => {
      void (async () => {
        // Delete this notification
        await api.delete('/notification', {
          params: {
            notification_id: notification.notification_id,
          },
        });
        // Notify the user about the successful deletion
        $q.notify({
          type: 'positive',
          message: 'Notification (' + notification.notification_title + ') deleted successfully',
        });
        // Fetch the updated notification list
        await fetchNotificationList();
      })();
    });
  } catch (error) {
    // Notify the user about the error
    $q.notify({
      type: 'negative',
      message: 'Failed to delete the notification: ' + String(error),
    });
  }
};

const utcTimeToLocalTime = (utcTime: string): string => {
  return new Date(utcTime).toLocaleString();
};

const fetchNotificationList = async () => {
  // Fetch notification list from the server
  const response = await api.get('/notification-list');
  // console.log('Raw response:', response.data);

  // Remove duplicates based on notification_id
  const uniqueNotifications = Array.from(
    new Map(response.data.map((item: NotificationItem) => [item.notification_id, item])).values(),
  ) as NotificationItem[];

  // Update the notificationList with the unique notifications
  notificationList.value = uniqueNotifications;

  // Sort the Notifications by created_at from newest to oldest
  notificationList.value.sort(
    (a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime(),
  );

  // Convert UTC time to local time
  notificationList.value.forEach((notification) => {
    notification.created_at = utcTimeToLocalTime(notification.created_at);
  });
};

onMounted(async () => {
  isFetching.value = true;
  // Check if the user is a teacher
  if (route.path.includes('teacher')) {
    isTeacher.value = true;
  } else {
    isTeacher.value = false;
  }
  // Fetch notification list
  await fetchNotificationList();
  isFetching.value = false;
});
</script>

<style scoped></style>
