|-src
| |
| |-api
| | |
| | |-login.ts #发送登录请求
| | |
| | |-register.ts #发送注册请求
| |
| |-boot
| | |
| | |-axios.ts #axios配置
| | |
| | |-i18n.ts #国际化配置
| |
| |-components
| | |
| | |-models.ts
| |
| |-config
| | |
| | |-modelNameOptions.ts #模型名称选项
| | |
| | |-whitelist.ts #白名单
| |
| |-css
| | |
| | |-quasar.variables.scss #quasar变量
| | |
| | |-app.scss #全局样式
| |
| |-i18n
| | |
| | |-index.ts #国际化配置
| | |
| | |-en-US
| | | |
| | | |-index.ts #英文国际化
| |
| |-layouts(*)
| | |
| | |-MainLayout.vue #主布局(没有使用)
| | |
| | |-StudentLayout.vue #学生布局(学生home page left side bar)
| | |
| | |-TeacherLayout.vue #教师布局(老师home page left side bar)
| | |
| | |-ChatLayout.vue #chatbot对话布局,左右上侧页边栏
| | |
| | |-ChatbotUsageLayout.vue #查看别人使用chatbot对话布局，左右上侧页边栏
| | |
| | |-AvatarLayout.vue #头像布局（不知道打开？）
| | |
| | |-AvatarLayoutTemp.vue #头像布局（旧版）（不知道打开？）
| |
| |-pages (*)
| | |
| | |-AboutPage.vue #关于页,网站介绍
| | |
| | |-AccountPage.vue #账户界面：账号信息，所属group（学生），修改密码
| | |
| | |-ActivationPage.vue #激活账号页
| | |
| | |-AvatarSessionPage.vue #头像会话页(???进不去界面)
| | |
| | |-ChatSharingPage.vue #分析链接打开后前往的页面
| | |
| | |-ErrorNotFound.vue #404报错界面
| | |
| | |-HomePage.vue #首页,能看到notification,老师可以创建notification
| | |
| | |-IndexPage.vue #没有加入路由，无作用
| | |
| | |-LoginPage.vue #登录页
| | |
| | |-RegisterPage.vue #注册页
| | |
| | |-SessionPage.vue #chatbot对话页
| | |
| | |-StartPage.vue #启动页，选择登录还是注册
| | |
| | |-Teacher
| | | |
| | | |-ChatbotUsageSessionPage.vue #查看chatbot不同session的对话记录
| | | |
| | | |-CoursePage.vue #课程页面，显示不同module及其chatbot,学生和老师的功能不同
| | | |
| | | |-CreateNewAvatarPage.vue #创建新头像页(?不确定功能)
| | | |
| | | |-CreateNewChatbotPage.vue #创建chatbot页,输入system prompt,welcome prompt,temperature,description,以及选择llm
| | | |
| | | |-CreateNewCoursePage.vue #创建新课程页
| | | |
| | | |-CreateNewNotificationPage.vue #老师创建新通知页
| | | |
| | | |-GenerateScript.vue #生成脚本页(?不清楚功能)
| | | |
| | | |-ManageAvatarPage.vue #管理头像页(?)
| | | |
| | | |-ManageChatbotPage.vue #管理,创造，复制chatbot页
| | | |
| | | |-ManageStudentPage.vue #管理group中的学生页
| | | |
| | | |-SettingAvatarPage.vue #设置头像页(?)
| | | |
| | | |-SettingChatbotPage.vue #设置chatbot页，对现有chatbot进行信息修改
| | | |
| | | |-ReviewChatbotUsagePage.vue #查看chatbot使用情况页
| | |
| | |-Student
| | | |
| | | |-CoursePage.vue #学生课程页
| | | |
| | | |-EnrollNewStudentGroupPage.vue #邀请学生加入新群组页
| |
| |-utils
| | |
| | |-notifications.ts #通知工具
| | |
| | |-session.ts #会话工具
| | 
| |-router(*)
| | |
| | |-index.ts #路由配置
| | |
| | |-routes.ts #路由配置
